#!/bin/bash

echo "快速重新安装应用..."

# 构建应用
echo "构建应用..."
cd android
./gradlew assembleDebug

if [ $? -eq 0 ]; then
    echo "构建成功，安装应用..."
    cd ..
    
    # 卸载旧版本
    adb uninstall com.snow
    
    # 安装新版本
    adb install android/app/build/outputs/apk/debug/app-debug.apk
    
    if [ $? -eq 0 ]; then
        echo "安装成功！"
        echo "请手动启用无障碍服务：设置 -> 无障碍 -> Snow -> 启用"
    else
        echo "安装失败"
    fi
else
    echo "构建失败"
fi
