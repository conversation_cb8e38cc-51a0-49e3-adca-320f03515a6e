import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  Alert,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import Voice from '@react-native-voice/voice';
import {useTheme} from '../context/ThemeContext';
import {useToast} from '../context/ToastContext';

const {width, height} = Dimensions.get('window');

interface VoiceRecordingScreenProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (result: VoiceResult) => void;
}

interface VoiceResult {
  amount: string;
  description: string;
  category: string;
  type: 'expense' | 'income';
}

const VoiceRecordingScreen: React.FC<VoiceRecordingScreenProps> = ({
  visible,
  onClose,
  onConfirm,
}) => {
  const {colors} = useTheme();
  const {showToast} = useToast();
  
  const [isRecording, setIsRecording] = useState(false);
  const [recordingText, setRecordingText] = useState('');
  const [parsedResult, setParsedResult] = useState<VoiceResult | null>(null);
  
  // 动画相关
  const waveAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // 设置语音识别事件监听
    const setupVoice = async () => {
      try {
        Voice.onSpeechStart = onSpeechStart;
        Voice.onSpeechRecognized = onSpeechRecognized;
        Voice.onSpeechEnd = onSpeechEnd;
        Voice.onSpeechError = onSpeechError;
        Voice.onSpeechResults = onSpeechResults;
      } catch (error) {
        console.error('语音初始化失败:', error);
      }
    };

    setupVoice();

    return () => {
      // 清理
      try {
        Voice.destroy().then(() => {
          Voice.removeAllListeners();
        });
      } catch (error) {
        console.error('语音清理失败:', error);
      }
    };
  }, []);

  useEffect(() => {
    if (visible) {
      // 页面显示时的淡入动画
      Animated.timing(fadeAnimation, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // 语音识别事件处理
  const onSpeechStart = () => {
    console.log('语音识别开始');
    setIsRecording(true);
    startWaveAnimation();
  };

  const onSpeechRecognized = () => {
    console.log('语音识别中');
  };

  const onSpeechEnd = () => {
    console.log('语音识别结束');
    setIsRecording(false);
    stopWaveAnimation();
  };

  const onSpeechError = (error: any) => {
    console.log('语音识别错误:', error);
    setIsRecording(false);
    stopWaveAnimation();
    showToast('语音识别失败，请重试', 'error');
  };

  const onSpeechResults = (event: any) => {
    console.log('语音识别结果:', event.value);
    if (event.value && event.value.length > 0) {
      const text = event.value[0];
      setRecordingText(text);
      
      // 解析语音内容
      const result = parseVoiceInput(text);
      if (result) {
        setParsedResult(result);
      }
    }
  };

  // 请求麦克风权限
  const requestMicrophonePermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: '麦克风权限',
            message: '语音记账需要使用麦克风来识别您的语音',
            buttonNeutral: '稍后询问',
            buttonNegative: '拒绝',
            buttonPositive: '允许',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('权限请求失败:', err);
        return false;
      }
    }
    return true; // iOS 权限在 Info.plist 中配置
  };

  // 开始/停止录音
  const toggleRecording = async () => {
    try {
      if (isRecording) {
        // 停止录音
        try {
          await Voice.stop();
        } catch (error) {
          console.log('停止语音识别失败，可能已经停止');
        }
        setIsRecording(false);
        stopWaveAnimation();
      } else {
        setRecordingText('');
        setParsedResult(null);

        // 请求麦克风权限
        const hasPermission = await requestMicrophonePermission();
        if (!hasPermission) {
          showToast('需要麦克风权限才能使用语音记账', 'error');
          return;
        }

        // 模拟语音识别（用于测试）
        setIsRecording(true);
        startWaveAnimation();

        // 3秒后模拟识别结果
        setTimeout(() => {
          const mockText = '记账 50元 午餐';
          setRecordingText(mockText);
          const result = parseVoiceInput(mockText);
          if (result) {
            setParsedResult(result);
          }
          setIsRecording(false);
          stopWaveAnimation();
        }, 3000);

        // 尝试启动真实的语音识别
        try {
          const isAvailable = await Voice.isAvailable();
          if (isAvailable) {
            await Voice.start('zh-CN');
          }
        } catch (error) {
          console.log('语音识别不可用，使用模拟模式');
        }
      }
    } catch (error) {
      console.error('语音操作失败:', error);
      setIsRecording(false);
      stopWaveAnimation();
      showToast('语音功能启动失败', 'error');
    }
  };

  // 波浪动画
  const startWaveAnimation = () => {
    const waveAnim = Animated.loop(
      Animated.sequence([
        Animated.timing(waveAnimation, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(waveAnimation, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    
    const pulseAnim = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnimation, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnimation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );
    
    waveAnim.start();
    pulseAnim.start();
  };

  const stopWaveAnimation = () => {
    waveAnimation.stopAnimation();
    pulseAnimation.stopAnimation();
    
    Animated.timing(waveAnimation, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
    
    Animated.timing(pulseAnimation, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // 解析语音输入
  const parseVoiceInput = (text: string): VoiceResult | null => {
    try {
      // 提取金额
      const amountMatch = text.match(/(\d+(?:\.\d{1,2})?)\s*[元块钱]?/);
      if (!amountMatch) {
        showToast('未识别到金额，请重新说话', 'warning');
        return null;
      }
      
      const amount = amountMatch[1];
      
      // 判断收入还是支出
      const isIncome = text.includes('收入') || text.includes('赚') || text.includes('工资') || text.includes('奖金');
      const type = isIncome ? 'income' : 'expense';
      
      // 提取描述（去掉金额和关键词）
      const description = text
        .replace(/记账|支出|花费|收入|赚|\d+(?:\.\d{1,2})?\s*[元块钱]?/g, '')
        .trim();
      
      // 智能分类
      const category = categorizeExpense(description);
      
      return {
        amount,
        description: description || '语音记账',
        category,
        type,
      };
    } catch (error) {
      console.error('解析语音输入失败:', error);
      return null;
    }
  };

  // 智能分类函数
  const categorizeExpense = (description: string): string => {
    const categoryKeywords = {
      '餐饮': ['午餐', '晚餐', '早餐', '夜宵', '咖啡', '奶茶', '火锅', '烧烤', '外卖', '食堂', '餐厅', '吃饭', '喝茶'],
      '交通': ['打车', '地铁', '公交', '出租车', '滴滴', '油费', '停车费', '高速费', '车费', '打的'],
      '娱乐': ['游乐场', '电影', 'KTV', '游戏', '演唱会', '旅游', '景点', '娱乐', '玩'],
      '购物': ['衣服', '鞋子', '化妆品', '超市', '淘宝', '京东', '商场', '买', '购物'],
      '生活': ['水电费', '房租', '话费', '网费', '理发', '洗衣', '生活', '日用品'],
      '医疗': ['看病', '买药', '体检', '医院', '药店', '医疗', '治疗'],
      '学习': ['书籍', '培训', '课程', '学费', '学习', '教育'],
      '运动': ['健身房', '瑜伽', '游泳', '跑步', '运动', '健身'],
    };

    const text = description.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categoryKeywords)) {
      for (const keyword of keywords) {
        if (text.includes(keyword)) {
          return category;
        }
      }
    }
    
    return '其他';
  };

  // 确认记账
  const handleConfirm = () => {
    if (parsedResult) {
      onConfirm(parsedResult);
      handleClose();
    }
  };

  // 关闭页面
  const handleClose = () => {
    if (isRecording) {
      Voice.stop();
    }
    setRecordingText('');
    setParsedResult(null);
    setIsRecording(false);
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="slide" statusBarTranslucent>
      <Animated.View style={[styles.container, {opacity: fadeAnimation}]}>
        {/* 顶部关闭按钮 */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Icon name="xmark" size={24} color="#666" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>语音记账</Text>
          <View style={{width: 40}} />
        </View>

        {/* 中央录音区域 */}
        <View style={styles.recordingArea}>
          {/* 语音波纹动画 */}
          <View style={styles.waveContainer}>
            {[...Array(4)].map((_, i) => (
              <Animated.View
                key={i}
                style={[
                  styles.wave,
                  {
                    transform: [{scale: waveAnimation}],
                    opacity: isRecording ? 0.6 - i * 0.15 : 0,
                  },
                  {
                    width: 120 + i * 30,
                    height: 120 + i * 30,
                    borderRadius: 60 + i * 15,
                  }
                ]}
              />
            ))}
            
            {/* 中央麦克风按钮 */}
            <Animated.View style={{transform: [{scale: pulseAnimation}]}}>
              <TouchableOpacity
                style={[
                  styles.micButton,
                  {backgroundColor: isRecording ? '#ff4444' : colors.primary},
                ]}
                onPress={toggleRecording}>
                <Icon 
                  name="microphone" 
                  size={40} 
                  color="#fff"
                />
              </TouchableOpacity>
            </Animated.View>
          </View>
          
          {/* 状态文本 */}
          <Text style={[styles.statusText, {color: colors.text}]}>
            {isRecording ? '正在聆听...' : '点击麦克风开始语音记账'}
          </Text>
          
          {/* 识别结果 */}
          {recordingText && (
            <View style={[styles.resultContainer, {backgroundColor: colors.card}]}>
              <Text style={[styles.resultLabel, {color: colors.textSecondary}]}>
                识别结果：
              </Text>
              <Text style={[styles.resultText, {color: colors.text}]}>
                {recordingText}
              </Text>
            </View>
          )}
          
          {/* 解析结果 */}
          {parsedResult && (
            <View style={[styles.parsedContainer, {backgroundColor: colors.primary + '10'}]}>
              <Text style={[styles.parsedLabel, {color: colors.primary}]}>
                智能解析：
              </Text>
              <Text style={[styles.parsedText, {color: colors.text}]}>
                {parsedResult.type === 'income' ? '收入' : '支出'} {parsedResult.amount}元
              </Text>
              <Text style={[styles.parsedText, {color: colors.text}]}>
                分类：{parsedResult.category}
              </Text>
              {parsedResult.description && (
                <Text style={[styles.parsedText, {color: colors.text}]}>
                  备注：{parsedResult.description}
                </Text>
              )}
            </View>
          )}
        </View>
        
        {/* 底部提示 */}
        <View style={styles.hintContainer}>
          <Text style={[styles.hintText, {color: colors.textSecondary}]}>
            💡 说出："记账 50元 午餐" 或 "收入 3000元 工资"
          </Text>
          <Text style={[styles.hintSubText, {color: colors.textSecondary}]}>
            系统会自动识别金额、分类和收支类型
          </Text>
        </View>
        
        {/* 底部按钮 */}
        <View style={styles.bottomButtons}>
          <TouchableOpacity 
            style={[styles.cancelButton, {borderColor: colors.border}]} 
            onPress={handleClose}>
            <Text style={[styles.cancelButtonText, {color: colors.textSecondary}]}>
              取消
            </Text>
          </TouchableOpacity>
          
          {parsedResult && (
            <TouchableOpacity 
              style={[styles.confirmButton, {backgroundColor: colors.primary}]} 
              onPress={handleConfirm}>
              <Text style={styles.confirmButtonText}>确认记账</Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  
  recordingArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  
  waveContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    marginBottom: 30,
  },
  
  wave: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  
  statusText: {
    fontSize: 18,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 20,
  },
  
  resultContainer: {
    width: width - 40,
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  
  resultLabel: {
    fontSize: 14,
    marginBottom: 5,
  },
  
  resultText: {
    fontSize: 16,
    lineHeight: 24,
  },
  
  parsedContainer: {
    width: width - 40,
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  
  parsedLabel: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  
  parsedText: {
    fontSize: 15,
    marginBottom: 4,
  },
  
  hintContainer: {
    paddingHorizontal: 20,
    paddingVertical: 15,
    alignItems: 'center',
  },
  
  hintText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 5,
  },
  
  hintSubText: {
    fontSize: 12,
    textAlign: 'center',
  },
  
  bottomButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 30,
    gap: 15,
  },
  
  cancelButton: {
    flex: 1,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  
  confirmButton: {
    flex: 1,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  confirmButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default VoiceRecordingScreen;
