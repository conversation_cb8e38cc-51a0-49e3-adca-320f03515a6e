import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Keyboard,
  Dimensions,
  ScrollView,
  Alert,
  Modal,
  TouchableWithoutFeedback,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {Carousel} from '@ant-design/react-native';
import PageContainer from '../components/PageContainer';
import CustomKeypad from '../components/CustomKeypad';
import DatePickerModal from '../components/DatePickerModal';
import databaseService, {Category, Family, AccountBook} from '../services/DatabaseService';
import {useFocusEffect} from '@react-navigation/native';
import ConfirmDialog from '../components/ConfirmDialog';
import {COLORS} from '../utils/color';
import {debounce} from '../utils/debounce';
import {useToast} from '../context/ToastContext';
import dayjs from 'dayjs';
import {useTheme} from '../context/ThemeContext';
const {width} = Dimensions.get('window');
const ITEMS_PER_PAGE = 12; // 每页显示12个分类

const DEFAULT_EXPENSE_CATEGORIES = [
  '餐饮',
  '购物',
  '交通',
  '住房',
  '娱乐',
  '医疗',
  '教育',
  '旅行',
  '通讯',
  '服装',
  '美容',
  '其他',
];
const DEFAULT_INCOME_CATEGORIES = [
  '工资',
  '奖金',
  '理财',
  '兼职',
  '礼金',
  '退款',
  '其他',
];

// 模糊匹配分类的辅助函数
const findFuzzyMatchCategory = (categories: Category[], targetCategory: string, transactionType: 'expense' | 'income') => {
  // 分类映射表
  const categoryMappings = {
    '餐饮': ['吃饭', '用餐', '食物', '饮食', '美食'],
    '交通': ['出行', '车费', '路费', '交通费'],
    '娱乐': ['玩乐', '游戏', '电影', '娱乐'],
    '购物': ['买东西', '购买', '商品', '物品'],
    '生活': ['日常', '生活用品', '家用'],
    '医疗': ['看病', '医院', '药品', '健康'],
    '学习': ['教育', '培训', '书籍', '课程'],
    '运动': ['健身', '锻炼', '体育'],
  };

  const filteredCategories = categories.filter(c =>
    c.isExpense === (transactionType === 'expense')
  );

  // 首先尝试直接匹配
  for (const category of filteredCategories) {
    if (category.name.includes(targetCategory) || targetCategory.includes(category.name)) {
      return category;
    }
  }

  // 然后尝试映射匹配
  for (const [categoryName, keywords] of Object.entries(categoryMappings)) {
    if (keywords.some(keyword => targetCategory.includes(keyword))) {
      const matchedCategory = filteredCategories.find(c => c.name === categoryName);
      if (matchedCategory) {
        return matchedCategory;
      }
    }
  }

  return null;
};

const AddTransactionScreen = ({route, navigation}) => {
  const {colors} = useTheme();
  // 使用全局 Toast
  const {showToast} = useToast();

  // 检查是否是编辑模式
  const isEditMode = route.params?.transaction !== undefined;
  const editTransaction = route.params?.transaction;
  const selectedDate = route.params?.selectedDate;
  const fromHome = route.params?.fromHome;

  // 从路由参数中获取支付相关信息
  const fromPayment = route.params?.autoDetected === true;
  const paymentType = route.params?.type || 'expense';
  const paymentAmount = route.params?.amount
    ? route.params.amount.toString()
    : '';
  const paymentPlatform = route.params?.platform || '';

  // 从路由参数中获取语音识别结果
  const voiceResult = route.params?.voiceResult;

  const [transactionType, setTransactionType] = useState<'expense' | 'income'>(
    isEditMode ? editTransaction.type :
    fromPayment ? paymentType :
    voiceResult ? voiceResult.type : 'expense',
  );
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(
    null,
  );
  const [amount, setAmount] = useState(
    isEditMode
      ? editTransaction.amount.toString()
      : fromPayment
      ? paymentAmount
      : voiceResult
      ? voiceResult.amount
      : '',
  );
  const [note, setNote] = useState(
    isEditMode
      ? editTransaction.note || ''
      : fromPayment
      ? `${paymentPlatform === 'alipay' ? '支付宝' : '微信'}支付`
      : voiceResult
      ? voiceResult.description
      : '',
  );
  const [date, setDate] = useState(
    isEditMode
      ? editTransaction.date
      : fromHome
      ? selectedDate
      : dayjs().format('YYYY-MM-DD'),
  ); // 今天的日期
  const [isCalculating, setIsCalculating] = useState(false);
  const [calculationBase, setCalculationBase] = useState('');
  const [currentOperator, setCurrentOperator] = useState('');
  const [isNoteInputFocused, setIsNoteInputFocused] = useState(false);
  const [showNoteInput, setShowNoteInput] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [expenseCategories, setExpenseCategories] = useState<Category[]>([]);
  const [incomeCategories, setIncomeCategories] = useState<Category[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const carouselRef = useRef(null);

  const noteInputRef = useRef(null);
  const [dateModalVisible, setDateModalVisible] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  // 添加确认对话框状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );

  // 添加历史备注状态
  const [categoryNotes, setCategoryNotes] = useState<string[]>([]);
  const [showNoteSuggestions, setShowNoteSuggestions] = useState(false);

  // 账本选择相关状态
  const [availableAccountBooks, setAvailableAccountBooks] = useState<AccountBook[]>([]);
  const [selectedAccountBook, setSelectedAccountBook] = useState<AccountBook | null>(null);
  const [accountBookSelectorVisible, setAccountBookSelectorVisible] = useState(false);

  // 新增状态：是否处于快速删除模式
  const [deleteMode, setDeleteMode] = useState(false);
  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(
    null,
  );

  // 新增待报销状态
  const [isReimbursable, setIsReimbursable] = useState(
    isEditMode && editTransaction ? !!editTransaction.isReimbursable : false,
  );

  // 处理待报销切换
  const handleReimbursableToggle = () => {
    setIsReimbursable(prev => !prev);
    setDeleteMode(false); // 如果在删除模式，退出删除模式
  };

  // 加载分类数据
  const loadCategories = async () => {
    try {
      const allCategories = await databaseService.getAllCategories();
      console.log('加载的分类:', allCategories);

      if (allCategories && allCategories.length > 0) {
        setCategories(allCategories);

        // 分离支出和收入分类
        const expenseCats = allCategories.filter(c => c.isExpense);
        const incomeCats = allCategories.filter(c => !c.isExpense);

        setExpenseCategories(expenseCats);
        setIncomeCategories(incomeCats);

        // 如果是编辑模式，查找并设置当前选中的分类
        if (isEditMode && editTransaction) {
          console.log('编辑模式，查找分类ID:', editTransaction.categoryId);
          console.log(
            '所有分类:',
            allCategories.map(c => ({id: c.id, name: c.name})),
          );

          const currentCategory = allCategories.find(
            c => String(c.id) === String(editTransaction.categoryId),
          );

          if (currentCategory) {
            console.log('找到匹配的分类:', currentCategory);
            setSelectedCategory(currentCategory);
          } else {
            console.log('未找到匹配的分类');
          }
        }
        // 如果是从支付宝/微信跳转过来，尝试根据平台选择合适的分类
        else if (fromPayment && paymentPlatform) {
          console.log('从支付平台跳转，查找合适分类:', paymentPlatform);

          // 根据平台选择默认分类（可以根据实际需求调整）
          let categoryName = '购物'; // 默认使用"购物"分类

          if (paymentPlatform === 'alipay') {
            categoryName = '购物';
          } else if (paymentPlatform === 'wechat') {
            categoryName = '购物';
          }

          // 查找匹配的分类
          const paymentCategory = allCategories.find(
            c =>
              c.name === categoryName &&
              c.isExpense === (transactionType === 'expense'),
          );

          if (paymentCategory) {
            console.log('找到匹配的支付分类:', paymentCategory.name);
            setSelectedCategory(paymentCategory);
          } else {
            // 如果找不到匹配的分类，使用第一个分类
            const firstCategory =
              transactionType === 'expense' ? expenseCats[0] : incomeCats[0];
            if (firstCategory) {
              console.log('使用默认分类:', firstCategory.name);
              setSelectedCategory(firstCategory);
            }
          }

          // 如果有备注，自动显示备注输入框
          // if (note) {
          //   setShowNoteInput(true);
          // }
        }
        // 如果是语音识别结果，根据识别的分类查找匹配的分类
        else if (voiceResult && voiceResult.category) {
          console.log('语音识别结果，查找分类:', voiceResult.category);

          // 查找匹配的分类
          const voiceCategory = allCategories.find(
            c =>
              c.name === voiceResult.category &&
              c.isExpense === (transactionType === 'expense'),
          );

          if (voiceCategory) {
            console.log('找到匹配的语音分类:', voiceCategory.name);
            setSelectedCategory(voiceCategory);
          } else {
            // 如果找不到完全匹配的分类，尝试模糊匹配
            const fuzzyCategory = findFuzzyMatchCategory(allCategories, voiceResult.category, transactionType);
            if (fuzzyCategory) {
              console.log('找到模糊匹配的分类:', fuzzyCategory.name);
              setSelectedCategory(fuzzyCategory);
            } else {
              // 使用默认分类
              const firstCategory =
                transactionType === 'expense' ? expenseCats[0] : incomeCats[0];
              if (firstCategory) {
                console.log('使用默认分类:', firstCategory.name);
                setSelectedCategory(firstCategory);
              }
            }
          }

          // 如果有备注，自动显示备注输入框
          if (note) {
            setShowNoteInput(true);
          }
        }
        // 正常添加模式下，默认选择餐饮分类
        else {
          // 查找餐饮分类
          const foodCategory = allCategories.find(
            c =>
              c.name === '餐饮' &&
              c.isExpense === (transactionType === 'expense'),
          );

          if (foodCategory) {
            setSelectedCategory(foodCategory);
          } else {
            // 如果找不到餐饮分类，使用第一个分类
            const firstCategory =
              transactionType === 'expense' ? expenseCats[0] : incomeCats[0];
            if (firstCategory) {
              setSelectedCategory(firstCategory);
            }
          }
        }

        // 筛选当前交易类型的分类
        const filteredCategories = allCategories.filter(category =>
          transactionType === 'expense'
            ? category.isExpense
            : !category.isExpense,
        );

        // 计算总页数
        const pages = Math.ceil(
          (filteredCategories.length + 1) / ITEMS_PER_PAGE,
        );
        setTotalPages(pages);
        setCurrentPage(0); // 重置到第一页
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  // 修改 useEffect 以监听交易类型变化
  useEffect(() => {
    // 当交易类型变化时，重新筛选分类并计算页数
    const filteredCategories = categories.filter(category =>
      transactionType === 'expense' ? category.isExpense : !category.isExpense,
    );

    if (filteredCategories.length > 0) {
      const pages = Math.ceil((filteredCategories.length + 1) / ITEMS_PER_PAGE);
      setTotalPages(pages);
      setCurrentPage(0);
    }
  }, [categories, transactionType]);

  // 在组件挂载时加载分类
  useEffect(() => {
    loadCategories();
  }, []);

  // 使用 useFocusEffect 监听页面获得焦点事件
  useFocusEffect(
    React.useCallback(() => {
      // 页面获得焦点时执行
      loadCategories();

      return () => {
        // 清理函数（如果需要）
      };
    }, []),
  );

  // 监听键盘事件
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        // 注释掉这两行，避免键盘隐藏时自动关闭备注输入框
        // setIsNoteInputFocused(false);
        // setShowNoteInput(false);
      },
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, []);

  // 处理页面变化
  const handlePageChange = index => {
    setCurrentPage(index);
  };

  // 渲染轮播页面
  const renderCarouselPages = () => {
    // 筛选当前交易类型的分类
    const filteredCategories = categories.filter(category =>
      transactionType === 'expense' ? category.isExpense : !category.isExpense,
    );

    return Array.from({length: totalPages}).map((_, pageIndex) => {
      const startIndex = pageIndex * ITEMS_PER_PAGE;
      const endIndex = startIndex + ITEMS_PER_PAGE;
      const pageCategories = filteredCategories.slice(startIndex, endIndex);

      // 如果是最后一页且有空间，添加"设置分类"按钮
      const isLastPage = pageIndex === totalPages - 1;
      const hasSpaceForSettings = pageCategories.length < ITEMS_PER_PAGE;

      return (
        <View key={pageIndex} style={styles.categoriesPage}>
          <View style={styles.categoriesGrid}>
            {pageCategories.map(renderCategoryItem)}
            {isLastPage && renderSettingsCategoryItem()}
          </View>
        </View>
      );
    });
  };

  // 处理数字按键
  const handleNumberPress = num => {
    setDeleteMode(false);
    if (isCalculating) {
      // 如果已经有运算符，则在后面追加数字
      if (currentOperator) {
        setAmount(amount + num);
      } else {
        // 如果还没有运算符，则替换整个数字
        setAmount(num);
        setCurrentOperator('');
      }
    } else {
      if (amount === '0') {
        setAmount(num);
      } else {
        setAmount(amount + num);
      }
    }
  };

  // 处理小数点
  const handleDotPress = () => {
    setDeleteMode(false);
    if (isCalculating && currentOperator) {
      // 如果正在计算且有运算符，检查运算符后面的数字是否已有小数点
      const parts = amount.split(currentOperator);
      if (parts.length > 1 && !parts[1].includes('.')) {
        setAmount(amount + '.');
      }
    } else {
      // 正常情况，检查整个数字是否已有小数点
      if (!amount.includes('.')) {
        setAmount(amount ? amount + '.' : '0.');
      }
    }
  };

  // 处理键盘删除按钮
  const handleKeypadDelete = () => {
    setDeleteMode(false);
    if (amount.length > 0) {
      const newAmount = amount.slice(0, -1);
      setAmount(newAmount);
    }
  };

  // 处理加减法
  const handleCalcPress = (oper: string) => {
    // 判断前面一个字符是否是数字
    if (!/^\d+$/.test(amount[amount.length - 1])) {
      return;
    }
    if (!amount) {
      setAmount('0');
    }
    if (!amount.includes('+') && !amount.includes('-')) {
      setAmount(amount + oper);
    } else {
      calculateResult(oper);
    }
    // 需要区分开来前一个运算符和当前运算符
    setCurrentOperator(oper);
  };

  // 处理计算结果
  const calculateResult = (oper: string, isFinal: boolean) => {
    if (currentOperator) {
      try {
        // 分割表达式
        const parts = amount.split(currentOperator);

        if (parts.length === 2) {
          const firstNumber = parseFloat(parts[0]);
          // 如果第二部分为空，使用0
          const secondNumber = parts[1] === '' ? 0 : parseFloat(parts[1]);

          if (!isNaN(firstNumber) && !isNaN(secondNumber)) {
            let result;
            if (currentOperator === '+') {
              result = firstNumber + secondNumber;
            } else if (currentOperator === '-') {
              result = firstNumber - secondNumber;

              // 检查结果是否小于0
              if (result < 0) {
                showToast('计算结果不能小于0', 'error');
                // 不修改输入，让用户自己调整
                return amount;
              }
            }

            // 格式化结果，避免JavaScript浮点数计算问题
            result = Math.round(result * 100) / 100;
            const resultStr = isFinal
              ? result.toString()
              : result.toString() + oper;
            setAmount(resultStr);
            setCurrentOperator('');
            return result.toString();
          }
        }
      } catch (error) {
        console.error('计算错误', error);
        setAmount('0');
        setCurrentOperator('');
      }
    }
    return amount;
  };

  // 处理交易类型切换
  const handleTypeChange = (type: 'expense' | 'income') => {
    setTransactionType(type);
    setSelectedCategory(null); // 切换类型时重置选中的分类
  };

  // 处理备注输入
  const handleNotePress = () => {
    setDeleteMode(false);
    setShowNoteInput(true);
    setIsNoteInputFocused(true);
    setShowNoteSuggestions(true); // 显示备注建议

    // 使用setTimeout确保组件已经渲染
    setTimeout(() => {
      if (noteInputRef.current) {
        noteInputRef.current.focus();
      }
    }, 100);
  };

  // 完成备注输入
  const handleNoteDone = () => {
    Keyboard.dismiss();
    setIsNoteInputFocused(false);
    setShowNoteInput(false);
    setShowNoteSuggestions(false); // 确保关闭建议列表
  };

  // 在选择分类时加载历史备注
  useEffect(() => {
    if (selectedCategory) {
      loadCategoryNotes();
    }
  }, [selectedCategory]);

  // 加载分类的历史备注
  const loadCategoryNotes = async () => {
    if (selectedCategory && selectedCategory.id) {
      try {
        const notes = await databaseService.getCategoryNotes(
          selectedCategory.id,
        );
        setCategoryNotes(notes);
      } catch (error) {
        console.error('加载分类备注失败', error);
      }
    }
  };

  // 处理选择历史备注
  const handleSelectNote = (selectedNote: string) => {
    setNote(selectedNote);
    // 不要在选择备注时隐藏建议列表，让用户可以继续选择
    // setShowNoteSuggestions(false);
  };

  // 在保存交易记录前检查是否超出预算
  const checkBudgetBeforeSave = async () => {
    if (transactionType === 'expense') {
      const amountValue = parseFloat(calculateResult(currentOperator, true));
      console.log('----amountValue', amountValue);

      // 修复编辑模式下的预算计算问题
      let amountToCheck = amountValue;

      // 如果是编辑模式，需要考虑原金额和新金额的差值
      if (isEditMode && editTransaction) {
        // 计算差值（新金额 - 原金额）
        const originalAmount = editTransaction.amount;
        const amountDifference = amountValue - originalAmount;

        // 如果差值小于等于0，说明没有增加支出，不需要检查预算
        if (amountDifference <= 0) {
          // 支出减少或不变，直接保存
          handleDonePress();
          return;
        }

        // 只检查增加的部分是否超出预算
        amountToCheck = amountDifference;
        console.log(
          '编辑模式 - 原金额:',
          originalAmount,
          '新金额:',
          amountValue,
          '差值:',
          amountDifference,
        );
      }

      const budgetCheck = await databaseService.checkBudgetExceeded({
        amount: amountToCheck,
        date,
      });

      if (budgetCheck.exceeded) {
        // 显示超额提醒
        let periodText = '';
        switch (budgetCheck.type) {
          case 'daily':
            periodText = '今日';
            break;
          case 'monthly':
            periodText = '本月';
            break;
          case 'yearly':
            periodText = '今年';
            break;
        }

        // // 使用 ConfirmDialog 显示预算超额提醒
        // setConfirmDialogMessage(
        //   `此笔支出将导致${periodText}总支出 ${budgetCheck.actual?.toFixed(
        //     2,
        //   )} 元，超出预算 ${
        //     budgetCheck.budget
        //   } 元。\n\n建议您注意消费习惯，合理规划支出。`,
        // );
        // setConfirmDialogAction(() => handleDonePress);
        // setConfirmDialogVisible(true);
        // 使用showTodast显示预算超额提醒
        showToast(
          `${periodText}总支出超出预算，请合理规划支出`,
          'warning',
          4000,
        );
      }
      handleDonePress(false);
    } else {
      // 收入类型，直接保存
      handleDonePress();
    }
  };

  // 修改完成按钮点击处理函数
  const debouncedHandleDonePress = debounce(() => {
    setDeleteMode(false);
    // 验证输入
    if (!amount || parseFloat(amount) === 0) {
      showToast('请输入金额', 'warning');
      return;
    }

    if (!selectedCategory) {
      showToast('请选择分类', 'warning');
      return;
    }

    // 检查预算超额
    checkBudgetBeforeSave();
  }, 300);

  // 修改保存交易记录的逻辑，添加保存备注的功能
  const handleDonePress = async (showDoneTip = true) => {
    // 验证输入
    if (!amount || parseFloat(amount) === 0) {
      showToast('请输入金额', 'warning');
      return;
    }

    if (!selectedCategory) {
      showToast('请选择分类', 'warning');
      return;
    }

    try {
      const parsedAmount = parseFloat(calculateResult(currentOperator, true));

      // 如果有备注，保存到分类备注历史
      if (note && note.trim() && selectedCategory.id) {
        await databaseService.saveCategoryNote(
          selectedCategory.id,
          note.trim(),
        );
      }

      if (isEditMode) {
        // 更新现有交易记录
        await databaseService.updateTransaction({
          id: editTransaction.id,
          amount: parsedAmount,
          note: note.trim(),
          date,
          categoryId: selectedCategory.id!,
          categoryName: selectedCategory.name,
          type: transactionType,
          familyId: null, // 移除家庭账单功能
          familyName: undefined,
          isReimbursable: isReimbursable,
          accountBookId: selectedAccountBook?.id,
          accountBookName: selectedAccountBook?.name,
        });
        console.log('--isReimbursable', isReimbursable);

        showDoneTip && showToast('交易记录已更新', 'success');
      } else {
        // 添加新交易记录
        await databaseService.addTransaction({
          amount: parsedAmount,
          note: note.trim(),
          date,
          categoryId: selectedCategory.id!,
          categoryName: selectedCategory.name,
          type: transactionType,
          familyId: null, // 移除家庭账单功能
          familyName: undefined,
          isReimbursable: isReimbursable,
          accountBookId: selectedAccountBook?.id,
          accountBookName: selectedAccountBook?.name,
        });

        showDoneTip && showToast('交易记录已添加', 'success');
      }

      // 立即返回，Toast 会继续显示
      navigation.goBack();
    } catch (error) {
      console.error('添加/更新交易记录失败', error);
      showToast(isEditMode ? '更新交易记录失败' : '添加交易记录失败', 'error');
    }
  };

  // 处理返回按钮点击
  const handleBackPress = () => {
    navigation.goBack();
  };

  // 处理删除交易
  const handleDeleteTransaction = () => {
    if (isEditMode && editTransaction) {
      setConfirmDialogMessage('确定要删除这笔交易吗？');
      setConfirmDialogAction(() => () => {
        databaseService
          .deleteTransaction(editTransaction.id)
          .then(() => {
            navigation.goBack();
          })
          .catch(error => {
            console.error('删除交易失败', error);
          });
      });
      setConfirmDialogVisible(true);
    }
  };

  // 处理删除分类
  const handleDeleteCategory = async (categoryId: string) => {
    try {
      await databaseService.deleteCategory(categoryId);
      setCategories(categories.filter(c => c.id !== categoryId));
      showToast('分类已删除', 'success');
    } catch (error) {
      showToast('删除失败', 'error');
    }
  };

  // 自定义左侧按钮
  const leftComponent = (
    <TouchableOpacity onPress={handleBackPress}>
      <Icon name="chevron-left" size={20} color="#007AFF" />
    </TouchableOpacity>
  );

  // 自定义右侧按钮
  const rightComponent = isEditMode ? (
    <TouchableOpacity onPress={handleDeleteTransaction}>
      <Icon name="trash" size={20} color="#FF3B30" />
    </TouchableOpacity>
  ) : !isEditMode && deleteMode ? (
    <TouchableOpacity onPress={() => setDeleteMode(false)}>
      <Text style={styles.rightCancel}>取消</Text>
    </TouchableOpacity>
  ) : null;

  // 渲染分类项
  const renderCategoryItem = category => {
    const isSelected = selectedCategory?.id === category.id;
    const iconColor = isSelected ? '#FFFFFF' : '#8E8E93';
    const backgroundColor = isSelected ? colors.primary : '#F2F2F7';

    // 判断是否为默认分类
    const isDefaultCategory =
      (category.isExpense &&
        DEFAULT_EXPENSE_CATEGORIES.includes(category.name)) ||
      (!category.isExpense &&
        DEFAULT_INCOME_CATEGORIES.includes(category.name));

    return (
      <TouchableOpacity
        key={category.id}
        style={styles.categoryItem}
        onPress={() => {
          if (!deleteMode) {
            setSelectedCategory(category);
          }
        }}
        onLongPress={() => {
          // 只有“添加模式”且非默认分类才允许进入删除模式
          if (!isEditMode && !isDefaultCategory) {
            setDeleteMode(true);
          }
        }}
        activeOpacity={0.7}>
        <View
          style={[
            styles.categoryIcon,
            {backgroundColor},
            isSelected && {
              ...styles.selectedCategoryIcon,
              backgroundColor: colors.primary,
            },
          ]}>
          <Icon name={category.icon} size={20} color={iconColor} />
          {/* 仅“添加模式”且非默认分类且处于删除模式时显示删除按钮 */}
          {deleteMode && !isDefaultCategory && !isEditMode && (
            <TouchableOpacity
              style={styles.deleteCategoryIconWrapper}
              onPress={() => handleDeleteCategory(category.id)}>
              <Icon name="xmark-circle" size={18} color="#FF3B30" />
            </TouchableOpacity>
          )}
        </View>
        <Text
          style={[
            styles.categoryName,
            isSelected && styles.selectedCategoryName,
          ]}
          numberOfLines={1}>
          {category.name}
        </Text>
        {/* 可选：默认分类可加个标识 */}
        {/* {isDefaultCategory && <Text style={styles.defaultCategoryTag}>默认</Text>} */}
      </TouchableOpacity>
    );
  };

  // 渲染"设置分类"按钮
  const renderSettingsCategoryItem = () => {
    return (
      <TouchableOpacity
        style={styles.categoryItem}
        onPress={() => {
          navigation.navigate('categorySettings');
          setDeleteMode(false);
        }}
        activeOpacity={0.7}>
        <View style={[styles.categoryIcon, {backgroundColor: '#F2F2F7'}]}>
          <Icon name="gear" size={20} color="#8E8E93" />
        </View>
        <Text style={styles.categoryName} numberOfLines={1}>
          设置分类
        </Text>
      </TouchableOpacity>
    );
  };

  // 渲染分页指示器
  const renderPagination = () => {
    return (
      <View style={styles.pagination}>
        {Array.from({length: totalPages}).map((_, index) => (
          <View
            key={index}
            style={[
              styles.paginationDot,
              currentPage === index && {
                ...styles.paginationDotActive,
                backgroundColor: colors.primary,
              },
            ]}
          />
        ))}
      </View>
    );
  };

  // 加载账本信息
  useEffect(() => {
    const loadAccountBooks = async () => {
      try {
        const accountBooks = await databaseService.getAllAccountBooks();
        setAvailableAccountBooks(accountBooks);

        // 如果是编辑模式且有账本ID，则设置选中的账本
        if (editTransaction && editTransaction.accountBookId) {
          const book = accountBooks.find(book => book.id === editTransaction.accountBookId);
          if (book) {
            setSelectedAccountBook(book);
          }
        } else {
          // 默认选择默认账本
          const defaultBook = accountBooks.find(book => book.isDefault);
          if (defaultBook) {
            setSelectedAccountBook(defaultBook);
          }
        }
      } catch (error) {
        console.error('加载账本信息失败', error);
      }
    };

    loadAccountBooks();
  }, [editTransaction]);

  // 账本选择器渲染函数
  const renderAccountBookSelector = () => {
    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>账本选择</Text>
        <TouchableOpacity
          style={styles.walletSelector}
          onPress={() => setAccountBookSelectorVisible(true)}>
          <View style={styles.walletSelectorLeft}>
            <Icon
              name="book"
              size={20}
              color={COLORS.primary}
              style={styles.walletIcon}
            />
            <Text style={styles.walletName}>
              {selectedAccountBook?.name || '请选择账本'}
            </Text>
          </View>
          <Icon name="chevron-down" size={16} color="#999999" />
        </TouchableOpacity>
      </View>
    );
  };

  // 账本选择模态框
  const renderAccountBookModal = () => (
    <Modal
      visible={accountBookSelectorVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setAccountBookSelectorVisible(false)}>
      <TouchableWithoutFeedback onPress={() => setAccountBookSelectorVisible(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.walletModalContent}>
            <Text style={styles.walletModalTitle}>选择账本</Text>

            {availableAccountBooks.map(book => (
              <TouchableOpacity
                key={book.id}
                style={[
                  styles.walletOption,
                  selectedAccountBook?.id === book.id && styles.selectedWalletOption,
                ]}
                onPress={() => {
                  setSelectedAccountBook(book);
                  setAccountBookSelectorVisible(false);
                }}>
                <Icon
                  name="book"
                  size={24}
                  color={
                    selectedAccountBook?.id === book.id ? COLORS.primary : '#666666'
                  }
                  style={styles.walletOptionIcon}
                />
                <View style={styles.walletOptionInfo}>
                  <View style={styles.accountBookTitleRow}>
                    <Text
                      style={[
                        styles.walletOptionName,
                        selectedAccountBook?.id === book.id &&
                          styles.selectedWalletOptionText,
                      ]}>
                      {book.name}
                    </Text>
                    {book.isDefault && (
                      <View style={styles.defaultBadgeSmall}>
                        <Text style={styles.defaultBadgeSmallText}>默认</Text>
                      </View>
                    )}
                  </View>
                  {book.description && (
                    <Text style={styles.walletOptionDesc}>
                      {book.description}
                    </Text>
                  )}
                </View>
                {selectedAccountBook?.id === book.id && (
                  <Icon name="check" size={20} color={COLORS.primary} />
                )}
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );

  return (
    <PageContainer
      headerTitle={isEditMode ? '编辑交易' : '添加交易'}
      rightComponent={rightComponent}
      backgroundColor={COLORS.secondary}>
      {/* 类型选择器 */}
      <View style={styles.typeSelector}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            transactionType === 'expense' && styles.activeTypeButton,
          ]}
          onPress={() => {
            if (transactionType !== 'expense') {
              setTransactionType('expense');
              setSelectedCategory(null); // 重置选中的分类
              // 自动选择第一个支出分类
              const expenseCats = categories.filter(c => c.isExpense);
              if (expenseCats.length > 0) {
                setSelectedCategory(expenseCats[0]);
              }
            }
            setDeleteMode(false);
          }}>
          <View style={styles.typeButtonContent}>
            <Icon
              name="arrow-down"
              size={16}
              color={COLORS.expense}
              style={styles.typeIcon}
            />
            <Text
              style={[
                styles.typeText,
                transactionType === 'expense' && styles.activeTypeText,
              ]}>
              支出
            </Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.typeButton,
            transactionType === 'income' && styles.activeTypeButton,
          ]}
          onPress={() => {
            if (transactionType !== 'income') {
              setTransactionType('income');
              setSelectedCategory(null); // 重置选中的分类
              // 自动选择第一个收入分类
              const incomeCats = categories.filter(c => !c.isExpense);
              if (incomeCats.length > 0) {
                setSelectedCategory(incomeCats[0]);
              }
            }
            setDeleteMode(false);
          }}>
          <View style={styles.typeButtonContent}>
            <Icon
              name="arrow-up"
              size={16}
              color={COLORS.income}
              style={styles.typeIcon}
            />
            <Text
              style={[
                styles.typeText,
                transactionType === 'income' && styles.activeTypeText,
              ]}>
              收入
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* 备注输入框 */}
      {showNoteInput && (
        <View style={styles.noteInputContainer}>
          <TextInput
            ref={noteInputRef}
            style={styles.noteInput}
            value={note}
            onChangeText={setNote}
            placeholder="请输入备注..."
            // autoFocus
          />
          <TouchableOpacity
            style={styles.noteInputDoneButton}
            onPress={handleNoteDone}>
            <Text style={styles.noteInputDoneText}>完成</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* 历史备注建议 */}
      {showNoteInput && showNoteSuggestions && categoryNotes.length > 0 && (
        <View style={styles.noteSuggestions}>
          <View style={styles.noteSuggestionsContainer}>
            {categoryNotes.map((historyNote, index) => (
              <TouchableOpacity
                key={index}
                style={styles.noteSuggestionItem}
                onPress={() => handleSelectNote(historyNote)}
                activeOpacity={0.7} // 增加点按反馈
              >
                <Text style={styles.noteSuggestionText} numberOfLines={1}>
                  {historyNote}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}

      {/* {renderAccountBookSelector()} */}

      {!isNoteInputFocused && (
        <>
          <View style={styles.content}>
            <View style={styles.categoriesContainer}>
              {/* <Text style={styles.sectionTitle}>选择分类</Text> */}

              {/* 分类滚动视图 - 替换轮播 */}
              <ScrollView
                style={styles.categoriesScrollView}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.categoriesScrollContent}>
                <View style={styles.categoriesGrid}>
                  {/* 设置分类按钮 - 放在最前面 */}
                  <TouchableOpacity
                    style={styles.categoryItem}
                    onPress={() => {
                      navigation.navigate('categorySettings');
                      setDeleteMode(false);
                    }}
                    onLongPress={() => {
                      // 只有“添加模式”且非默认分类才允许进入删除模式
                      if (!isEditMode && !isDefaultCategory) {
                        setDeleteMode(true);
                      }
                    }}
                    activeOpacity={0.7}>
                    <View
                      style={[
                        styles.categoryIcon,
                        {backgroundColor: '#F2F2F7'},
                      ]}>
                      <Icon name="gear" size={20} color="#8E8E93" />
                    </View>
                    <Text style={styles.categoryName} numberOfLines={1}>
                      设置分类
                    </Text>
                  </TouchableOpacity>

                  {/* 分类列表 */}
                  {categories
                    .filter(category =>
                      transactionType === 'expense'
                        ? category.isExpense
                        : !category.isExpense,
                    )
                    .map(renderCategoryItem)}
                </View>
              </ScrollView>

              {/* {renderPagination()} */}
            </View>
          </View>

          {/* 自定义数字键盘 */}
          <CustomKeypad
            amount={amount}
            note={note}
            date={date}
            isReimbursable={isReimbursable} // 新增
            onReimbursableToggle={handleReimbursableToggle} // 新增
            onNumberPress={handleNumberPress}
            onDotPress={handleDotPress}
            onDeletePress={handleKeypadDelete}
            onPlusPress={() => {
              handleCalcPress('+');
              setDeleteMode(false);
            }}
            onMinusPress={() => {
              handleCalcPress('-');
              setDeleteMode(false);
            }}
            onDonePress={debouncedHandleDonePress}
            onNotePress={handleNotePress}
            onDatePress={() => {
              setDeleteMode(false);
              setDateModalVisible(true);
            }}
            onWalletPress={() => {
              setDeleteMode(false);
              setAccountBookSelectorVisible(true);
            }}
          />
        </>
      )}

      {/* 日期选择弹窗 */}
      <DatePickerModal
        visible={dateModalVisible}
        selectedDate={date}
        onClose={() => setDateModalVisible(false)}
        onSelectDate={setDate}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onCancel={() => setConfirmDialogVisible(false)}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        cancelText="取消"
        confirmText="确定"
      />

      {renderAccountBookModal()}
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  rightCancel: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
  },
  typeSelector: {
    flexDirection: 'row',
    marginHorizontal: 20,
    marginVertical: 15,
    borderRadius: 10,
    backgroundColor: COLORS.background.light,
    padding: 3,
    alignSelf: 'center',
    width: 'auto',
    minWidth: 200,
    maxWidth: width * 0.6,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  typeButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  typeIcon: {
    marginRight: 6,
  },
  activeTypeButton: {
    backgroundColor: COLORS.background.white,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    elevation: 1,
  },
  typeText: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.gray,
  },
  activeTypeText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: COLORS.background.white,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  amountPrefix: {
    fontSize: 16,
    color: COLORS.text.gray,
  },
  amountText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
  },
  noteInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: COLORS.background.white,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  noteInput: {
    flex: 1,
    height: 40,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
  },
  noteInputDoneButton: {
    marginLeft: 10,
    padding: 10,
  },
  noteInputDoneText: {
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '500',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 15,
  },
  categoriesContainer: {
    marginBottom: 20,
  },
  carousel: {
    paddingTop: 6,
    width: width - 30,
    height: 300, // 根据您的分类项高度调整
  },
  categoriesPage: {
    paddingTop: 6,
    width: width - 30,
    height: 300, // 与 carousel 高度一致
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  categoryItem: {
    width: (width - 30) / 4, // 每行4个
    alignItems: 'center',
    marginBottom: 15,
  },
  categoryIcon: {
    width: 50,
    height: 50,
    borderRadius: 12, // 使用圆角矩形，符合 iOS 14+ 的设计语言
    backgroundColor: COLORS.background.light, // iOS 系统浅灰色
    marginBottom: 6,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 1,
    // elevation: 1,
  },
  selectedCategoryIcon: {
    borderWidth: 3,
    borderColor: COLORS.primary,
  },
  categoryName: {
    fontSize: 12,
    color: COLORS.text.gray, // iOS 系统次要文本颜色
    textAlign: 'center',
    width: '100%',
  },
  selectedCategoryName: {
    color: COLORS.primary, // iOS 系统蓝色
    fontWeight: '500',
  },
  selectedCategoryItem: {
    // 选中状态样式 - 保持简洁
  },
  content: {
    flex: 1,
    padding: 15,
    backgroundColor: COLORS.background.white,
  },
  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
    height: 20, // 固定高度，避免指示器切换时导致布局跳动
  },
  paginationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.background.light,
    marginHorizontal: 3,
    opacity: 0.5, // 未选中的点设置半透明
  },
  paginationDotActive: {
    opacity: 1, // 选中的点完全不透明
    transform: [{scale: 1.2}], // 选中的点稍微放大
  },
  headerRightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  deleteButton: {
    marginRight: 16,
  },
  doneButtonText: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '600',
  },
  noteSuggestions: {
    backgroundColor: COLORS.background.white,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: '#DDDDDD',
    marginBottom: 5,
  },
  noteSuggestionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingRight: 5,
    paddingLeft: 5,
  },
  noteSuggestionItem: {
    backgroundColor: '#F2F2F7', // 使用iOS风格的浅灰色背景
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 20, // 使用更圆润的边角
    marginRight: 10,
    marginBottom: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
    maxWidth: '45%', // 限制最大宽度，确保一行至少能显示两个
    alignItems: 'center', // 文字居中
  },
  noteSuggestionText: {
    fontSize: 15,
    fontWeight: '500',
    color: COLORS.text.primary,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  walletSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.background.light,
    borderRadius: 12,
    padding: 15,
    marginTop: 10,
  },
  walletSelectorLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  walletIcon: {
    marginRight: 12,
  },
  walletName: {
    fontSize: 16,
    color: COLORS.text.primary,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  walletModalContent: {
    width: '90%',
    backgroundColor: COLORS.background.white,
    borderRadius: 12,
    padding: 20,
  },
  walletModalTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  walletOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: COLORS.background.light,
  },
  selectedWalletOption: {
    backgroundColor: 'rgba(33, 150, 243, 0.1)',
  },
  walletOptionIcon: {
    marginRight: 16,
  },
  walletOptionInfo: {
    flex: 1,
  },
  walletOptionName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text.primary,
    marginBottom: 4,
  },
  selectedWalletOptionText: {
    color: COLORS.primary,
  },
  walletOptionDesc: {
    fontSize: 14,
    color: COLORS.text.gray,
  },
  deleteCategoryIconWrapper: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 2,
    zIndex: 10,
    elevation: 2,
  },
  displayContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: COLORS.background.white,
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  leftSection: {
    flex: 1,
    flexDirection: 'column',
    marginRight: 10,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteContainer: {
    marginBottom: 8,
  },
  reimbursableButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 10,
    borderRadius: 16,
    backgroundColor: COLORS.background.light,
    alignSelf: 'flex-start',
  },
  reimbursableButtonActive: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  reimbursableText: {
    fontSize: 14,
    color: COLORS.text.gray,
    marginLeft: 4,
  },
  reimbursableTextActive: {
    color: COLORS.primary,
    fontWeight: '500',
  },
  // 新的分类滚动视图样式
  categoriesScrollView: {
    maxHeight: 320,
    marginBottom: 10,
  },
  categoriesScrollContent: {
    paddingBottom: 10,
  },
  accountBookTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  defaultBadgeSmall: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 3,
    marginLeft: 6,
  },
  defaultBadgeSmallText: {
    fontSize: 8,
    color: '#FFFFFF',
    fontWeight: '500',
  },
});

export default AddTransactionScreen;
