import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Modal,
  TouchableWithoutFeedback,
  Alert,
  Platform,
  ActionSheetIOS,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useFocusEffect} from '@react-navigation/native';
import CustomTabBar from '../components/CustomTabBar';
import CustomCalendar from '../components/CustomCalendar';
import PageContainer from '../components/PageContainer';
import VoiceRecordingScreen from './VoiceRecordingScreen';
import VoiceGuideModal from '../components/VoiceGuideModal';
import databaseService, {
  Transaction,
  Category,
} from '../services/DatabaseService';
import dayjs from 'dayjs';
import {COLORS} from '../utils/color';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CustomModal from '../components/CustomModal';
import ConfirmDialog from '../components/ConfirmDialog';
import {
  analyzeConsumptionBehavior,
  calculateHealthScore,
  generateSmartSuggestions,
} from '../utils/algorithm';
import {useTheme} from '../context/ThemeContext';

const HomeScreen = ({navigation}) => {
  const {colors} = useTheme();
  const today = new Date().toISOString().split('T')[0]; // 格式: YYYY-MM-DD
  const [selectedDate, setSelectedDate] = useState(today);
  const [activeTab, setActiveTab] = useState('bills');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [transactionDates, setTransactionDates] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [monthlyStats, setMonthlyStats] = useState({income: 0, expense: 0});
  const [dailyStats, setDailyStats] = useState({
    income: 0,
    expense: 0,
    balance: 0,
  });
  const [showMonthlyAnalysis, setShowMonthlyAnalysis] = useState(false);
  const [monthlyAnalysisData, setMonthlyAnalysisData] = useState(null);
  const [showMonthlyAnalysisNotice, setShowMonthlyAnalysisNotice] =
    useState(false);
  const [showMonthlyStats, setShowMonthlyStats] = useState(false);
  const [familyName, setFamilyName] = useState('');
  const [yearMonthPickerVisible, setYearMonthPickerVisible] = useState(false);
  const [availableYears, setAvailableYears] = useState<number[]>([]);
  const [tempYear, setTempYear] = useState(dayjs(selectedDate).year());
  // 添加确认对话框状态
  const [confirmDialogVisible, setConfirmDialogVisible] = useState(false);
  const [confirmDialogMessage, setConfirmDialogMessage] = useState('');
  const [confirmDialogAction, setConfirmDialogAction] = useState<() => void>(
    () => {},
  );
  const calendarRef = useRef(null);

  // 语音记账相关状态
  const [voiceRecordingVisible, setVoiceRecordingVisible] = useState(false);
  const [voiceGuideVisible, setVoiceGuideVisible] = useState(false);

  // 使用 useFocusEffect 确保每次页面获得焦点时都重新加载数据
  useFocusEffect(
    React.useCallback(() => {
      console.log('HomeScreen 获得焦点，重新加载数据');
      loadData();
      loadTransactionDates();
      checkMonthlyAnalysis();
      loadFamilyInfo();
    }, [selectedDate]),
  );

  // 初始加载
  useEffect(() => {
    loadData();
    loadTransactionDates();
    checkVoiceGuide();
  }, []);

  // 检查是否显示语音引导
  const checkVoiceGuide = async () => {
    try {
      const hasSeenGuide = await AsyncStorage.getItem('hasSeenVoiceGuide');
      if (!hasSeenGuide) {
        // 延迟显示，让页面先加载完成
        setTimeout(() => {
          setVoiceGuideVisible(true);
        }, 1000);
      }
    } catch (error) {
      console.error('检查语音引导状态失败:', error);
    }
  };

  // 关闭语音引导
  const handleCloseVoiceGuide = async () => {
    setVoiceGuideVisible(false);
    try {
      await AsyncStorage.setItem('hasSeenVoiceGuide', 'true');
    } catch (error) {
      console.error('保存语音引导状态失败:', error);
    }
  };

  // 在组件中添加事件监听
  useEffect(() => {
    // 监听家庭名称更新事件
    const handleFamilyNameUpdated = ({familyId, name}) => {
      // 强制重新加载交易记录数据
      loadTransactions();
    };

    // 注册事件监听
    databaseService.eventEmitter?.addListener(
      'familyNameUpdated',
      handleFamilyNameUpdated,
    );

    // 组件卸载时清理
    return () => {
      databaseService.eventEmitter?.removeListener(
        'familyNameUpdated',
        handleFamilyNameUpdated,
      );
    };
  }, []);

  // 获取所有可用年份
  useEffect(() => {
    const fetchYears = async () => {
      const years = await databaseService.getTransactionYears();
      const currentYear = new Date().getFullYear();
      const recentYears = [];
      for (let i = 0; i < 10; i++) {
        recentYears.push(currentYear - i);
      }
      const allYears = [...new Set([...years, ...recentYears])].sort(
        (a, b) => b - a,
      );
      setAvailableYears(allYears);
    };
    fetchYears();
  }, []);

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    try {
      // 加载分类
      const allCategories = await databaseService.getAllCategories();
      setCategories(allCategories);
      // 加载选定日期的交易记录
      const dateTransactions = await databaseService.getTransactionsByDate(
        selectedDate,
      );
      console.log('----dateTransactions', dateTransactions);
      setTransactions(dateTransactions);

      // 计算当日总计
      let dailyIncome = 0;
      let dailyExpense = 0;

      dateTransactions.forEach(transaction => {
        const category = allCategories.find(
          c => c.id === transaction.categoryId,
        );
        if (category?.isExpense) {
          dailyExpense += transaction.amount;
        } else {
          dailyIncome += transaction.amount;
        }
      });

      setDailyStats({
        income: dailyIncome,
        expense: dailyExpense,
        balance: dailyIncome - dailyExpense,
      });

      // 加载月度统计
      const date = dayjs(selectedDate);
      const stats = await databaseService.getMonthlyStatistics(
        date.year(),
        date.month() + 1,
      );
      setMonthlyStats(stats);
    } catch (error) {
      console.error('加载数据失败', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载交易日期数据的函数
  const loadTransactionDates = async () => {
    try {
      console.log('加载交易日期数据');
      const dates = await databaseService.getTransactionDates();
      console.log('交易日期数据:', dates);
      setTransactionDates(dates);
    } catch (error) {
      console.error('加载交易日期失败', error);
    }
  };

  // 获取分类
  const getCategory = (categoryId: string) => {
    return categories.find(c => c.id === categoryId) || null;
  };

  // 处理日期选择
  const handleSelectDate = date => {
    setSelectedDate(date);
  };

  // 处理Tab点击
  const handleTabPress = tabKey => {
    if (tabKey === 'bills') {
      navigation.navigate('bills');
    } else if (tabKey === 'add') {
      navigation.navigate('addTransaction', {
        selectedDate: selectedDate,
        fromHome: true,
      });
    } else if (tabKey === 'profile') {
      navigation.navigate('profile');
    }
  };

  // 处理长按 - 语音记账
  const handleTabLongPress = (tabKey: string) => {
    if (tabKey === 'voice') {
      setVoiceRecordingVisible(true);
    }
  };

  // 处理语音记账结果
  const handleVoiceResult = (result: any) => {
    // 导航到添加交易页面，并传递语音识别的结果
    navigation.navigate('addTransaction', {
      selectedDate: selectedDate,
      fromHome: true,
      voiceResult: result,
    });
  };

  // 关闭语音记账页面
  const handleCloseVoiceRecording = () => {
    setVoiceRecordingVisible(false);
  };

  // 定义Tab项
  const tabs = [
    {key: 'bills', title: '账单', icon: 'snowflake'},
    {key: 'add', title: '记账', icon: 'plus'},
    {key: 'profile', title: '我的', icon: 'fish'},
  ];

  // 渲染加载状态
  const renderLoading = () => (
    <View style={styles.emptyContainer}>
      <ActivityIndicator size="large" color="#007AFF" />
    </View>
  );

  // 自定义右侧按钮
  const rightComponent = (
    <TouchableOpacity onPress={() => console.log('设置按钮点击')}>
      <Icon name="settings-outline" size={24} color="#007AFF" />
    </TouchableOpacity>
  );

  // 添加日期格式化函数
  const formatDate = dateString => {
    const date = dayjs(dateString);
    return date.format('MM/DD');
  };

  // 处理交易记录项点击
  const handleTransactionPress = transaction => {
    navigation.navigate('addTransaction', {transaction});
  };

  // 添加删除交易记录的函数
  const handleDeleteTransaction = async transactionId => {
    try {
      await databaseService.deleteTransaction(Number(transactionId));
      // 删除后重新加载数据
      loadData();
      loadTransactionDates();
    } catch (error) {
      console.error('删除交易失败', error);
    }
  };

  // 显示操作菜单的函数
  const showActionSheet = item => {
    setConfirmDialogMessage('是否确认删除该账单？');
    setConfirmDialogAction(() => () => {
      handleDeleteTransaction(item.id);
    });
    setConfirmDialogVisible(true);
  };

  // 添加检查是否应该显示月度分析的函数
  const checkMonthlyAnalysis = async () => {
    try {
      const analysisToday = new Date();
      const isFirstDayOfMonth = analysisToday.getDate() === 1;

      if (!isFirstDayOfMonth) {
        return; // 如果不是月初第一天，不显示分析
      }

      // 获取当前日期的字符串表示，用于检查今天是否已经显示过
      const dateString = analysisToday.toISOString().split('T')[0];
      const lastShownDate = await AsyncStorage.getItem(
        'lastMonthlyAnalysisDate',
      );

      // 如果今天已经显示过，不再显示
      if (lastShownDate === dateString) {
        return;
      }

      // 计算上个月的年份和月份
      let prevMonth = analysisToday.getMonth();
      let prevYear = analysisToday.getFullYear();

      if (prevMonth === 0) {
        prevMonth = 12;
        prevYear -= 1;
      }

      // 获取上个月的交易数据
      const startDate = `${prevYear}-${String(prevMonth).padStart(2, '0')}-01`;
      const endDate = `${prevYear}-${String(prevMonth).padStart(2, '0')}-31`;

      const allTransactions = await databaseService.getAllTransactions();
      const transactions = allTransactions.filter(t => {
        return t.date >= startDate && t.date <= endDate;
      });

      // 如果没有交易数据，不显示分析
      if (transactions.length === 0) {
        return;
      }

      // 获取所有分类
      const categories = await databaseService.getAllCategories();

      // 计算月度统计数据
      const {income, expense} = await databaseService.getMonthlyStatistics(
        prevYear,
        prevMonth,
      );

      const monthlyTotal = {
        income,
        expense,
        balance: income - expense,
      };

      // 分析消费行为
      const behaviorAnalysis = analyzeConsumptionBehavior(
        transactions,
        categories,
      );

      // 计算消费健康度评分
      const healthScore = calculateHealthScore(transactions, monthlyTotal);

      // 生成智能建议
      const suggestions = generateSmartSuggestions(
        transactions,
        categories,
        behaviorAnalysis,
        healthScore,
        monthlyTotal,
      );

      // 设置分析数据
      setMonthlyAnalysisData({
        prevYear,
        prevMonth,
        behaviorAnalysis,
        healthScore,
        suggestions,
        transactions,
        categories,
        monthlyTotal,
      });

      // 先显示提示弹窗，而不是直接显示分析
      setShowMonthlyAnalysisNotice(true);

      // 记录已经显示过分析的日期
      await AsyncStorage.setItem('lastMonthlyAnalysisDate', dateString);
    } catch (error) {
      console.error('检查月度分析失败', error);
    }
  };

  // 处理查看月度分析的函数
  const handleViewMonthlyAnalysis = () => {
    setShowMonthlyAnalysisNotice(false);
    setTimeout(() => {
      setShowMonthlyAnalysis(true);
    }, 300); // 短暂延迟，让通知弹窗先关闭
  };

  // 关闭通知弹窗的函数
  const closeMonthlyAnalysisNotice = () => {
    setShowMonthlyAnalysisNotice(false);
  };

  // 渲染月度分析通知弹窗
  const renderMonthlyAnalysisNotice = () => {
    if (!monthlyAnalysisData) {
      return null;
    }

    const {prevYear, prevMonth, healthScore} = monthlyAnalysisData;

    // 月份名称
    const monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];

    const monthName = monthNames[prevMonth - 1];

    // 根据健康分数选择不同的表情和颜色
    let emoji = '😊';
    let color = '#4CAF50';
    let message = '你的财务状况非常健康！';

    if (healthScore.totalScore < 60) {
      emoji = '😟';
      color = '#FF3B30';
      message = '你的财务状况需要改善！';
    } else if (healthScore.totalScore < 80) {
      emoji = '🙂';
      color = '#FF9500';
      message = '你的财务状况良好，但仍有提升空间！';
    }

    return (
      <CustomModal
        visible={showMonthlyAnalysisNotice}
        onClose={closeMonthlyAnalysisNotice}
        animationType="fade"
        position="center"
        customStyle={styles.noticeModalStyle}>
        <View style={styles.noticeContainer}>
          <Text style={styles.noticeEmoji}>{emoji}</Text>
          <Text style={styles.noticeTitle}>月度账单分析已生成！</Text>
          <Text style={styles.noticeSubtitle}>
            {prevYear}年{monthName}的账单分析已准备就绪
          </Text>

          <View style={styles.noticeScoreContainer}>
            <Text style={styles.noticeScoreLabel}>财务健康分数</Text>
            <View style={styles.noticeScoreCircle}>
              <Text style={[styles.noticeScoreValue, {color}]}>
                {healthScore.totalScore}
              </Text>
            </View>
            <Text style={[styles.noticeMessage, {color}]}>{message}</Text>
          </View>

          <TouchableOpacity
            style={styles.noticeButton}
            onPress={handleViewMonthlyAnalysis}>
            <Text style={styles.noticeButtonText}>查看详细分析</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.noticeLaterButton}
            onPress={closeMonthlyAnalysisNotice}>
            <Text style={styles.noticeLaterButtonText}>稍后查看</Text>
          </TouchableOpacity>
        </View>
      </CustomModal>
    );
  };

  // 关闭月度分析的函数
  const closeMonthlyAnalysis = () => {
    setShowMonthlyAnalysis(false);
  };

  // 渲染月度分析报告
  const renderMonthlyAnalysis = () => {
    if (!monthlyAnalysisData) {
      return null;
    }

    const {
      prevYear,
      prevMonth,
      behaviorAnalysis,
      healthScore,
      suggestions,
      monthlyTotal,
    } = monthlyAnalysisData;

    // 月份名称
    const monthNames = [
      '一月',
      '二月',
      '三月',
      '四月',
      '五月',
      '六月',
      '七月',
      '八月',
      '九月',
      '十月',
      '十一月',
      '十二月',
    ];

    const monthName = monthNames[prevMonth - 1];

    return (
      <CustomModal
        visible={showMonthlyAnalysis}
        onClose={closeMonthlyAnalysis}
        animationType="slide"
        position="bottom"
        closeOnBackdropPress={false} // 不允许点击蒙层关闭
        customStyle={styles.monthlyAnalysisModalStyle}>
        <View style={styles.monthlyAnalysisHeader}>
          <View style={styles.monthlyAnalysisHeaderLeft}>
            <Text style={styles.monthlyAnalysisTitle}>
              {prevYear}年{monthName}月度账单分析
            </Text>
            <Text style={styles.monthlyAnalysisSubtitle}>
              每月账单智能分析报告
            </Text>
          </View>
          <TouchableOpacity
            style={styles.monthlyAnalysisCloseButton}
            onPress={closeMonthlyAnalysis}>
            <Icon name="xmark" size={20} color="#666666" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.monthlyAnalysisContent}>
          {/* 账单封面 */}
          <View style={styles.monthlyAnalysisCover}>
            <Icon name="file-invoice-dollar" size={50} color="#007AFF" />
            <Text style={styles.monthlyAnalysisCoverTitle}>
              {prevYear}年{monthName}月度账单
            </Text>
            <Text style={styles.monthlyAnalysisCoverSubtitle}>
              财务健康评分
            </Text>
            <View style={styles.healthScoreContainer}>
              <Text style={styles.healthScoreText}>
                {healthScore.totalScore}
              </Text>
              <Text style={styles.healthScoreMax}>/100</Text>
            </View>
            <View style={styles.healthScoreDesc}>
              <Text style={styles.healthScoreDescText}>
                {healthScore.scoreDescription}
              </Text>
            </View>
          </View>

          {/* 消费概览 */}
          <View style={styles.monthlyAnalysisSection}>
            <Text style={styles.monthlyAnalysisSectionTitle}>消费概览</Text>
            <View style={styles.monthlyAnalysisSummaryRow}>
              <View style={styles.monthlyAnalysisSummaryItem}>
                <Text style={styles.monthlyAnalysisSummaryValue}>
                  ￥{monthlyTotal.expense.toFixed(0)}
                </Text>
                <Text style={styles.monthlyAnalysisSummaryLabel}>
                  月度总支出
                </Text>
              </View>
              <View style={styles.monthlyAnalysisSummaryItem}>
                <Text style={styles.monthlyAnalysisSummaryValue}>
                  ￥{monthlyTotal.income.toFixed(0)}
                </Text>
                <Text style={styles.monthlyAnalysisSummaryLabel}>
                  月度总收入
                </Text>
              </View>
              <View style={styles.monthlyAnalysisSummaryItem}>
                <Text
                  style={[
                    styles.monthlyAnalysisSummaryValue,
                    monthlyTotal.balance >= 0
                      ? styles.positiveBalance
                      : styles.negativeBalance,
                  ]}>
                  ￥{Math.abs(monthlyTotal.balance).toFixed(0)}
                </Text>
                <Text style={styles.monthlyAnalysisSummaryLabel}>
                  {monthlyTotal.balance >= 0 ? '结余' : '超支'}
                </Text>
              </View>
            </View>
          </View>

          {/* 消费行为特征 */}
          <View style={styles.monthlyAnalysisSection}>
            <Text style={styles.monthlyAnalysisSectionTitle}>消费行为特征</Text>

            {/* 消费类型分布 */}
            <Text style={styles.monthlyAnalysisSubtitle}>消费类型分布</Text>
            <View style={styles.behaviorTraitContainer}>
              {behaviorAnalysis.topCategories.map((category, index) => (
                <View key={`category-${index}`} style={styles.categoryItem}>
                  <View style={styles.categoryProgress}>
                    <View
                      style={[
                        styles.categoryProgressFill,
                        {
                          width: `${category.percentage}%`,
                          backgroundColor: category.color || '#007AFF',
                        },
                      ]}
                    />
                  </View>
                  <View style={styles.categoryInfo}>
                    <Text style={styles.categoryName}>{category.name}</Text>
                    <Text style={styles.categoryPercentage}>
                      {category.percentage.toFixed(1)}%
                    </Text>
                  </View>
                  <Text style={styles.categoryAmount}>
                    ￥{category.amount.toFixed(0)}
                  </Text>
                </View>
              ))}
            </View>

            {/* 消费习惯特征 */}
            <Text style={styles.monthlyAnalysisSubtitle}>消费习惯特征</Text>
            <View style={styles.traitsContainer}>
              {behaviorAnalysis.traits.map((trait, index) => (
                <View key={`trait-${index}`} style={styles.traitItem}>
                  <Icon
                    name={trait.icon}
                    size={20}
                    color="#007AFF"
                    style={styles.traitIcon}
                  />
                  <View style={styles.traitContent}>
                    <Text style={styles.traitTitle}>{trait.title}</Text>
                    <Text style={styles.traitDescription}>
                      {trait.description}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* 健康度评分详情 */}
          <View style={styles.monthlyAnalysisSection}>
            <Text style={styles.monthlyAnalysisSectionTitle}>
              财务健康度评分
            </Text>

            {healthScore.factors.map((factor, index) => (
              <View key={`factor-${index}`} style={styles.scoreFactorItem}>
                <View style={styles.scoreFactorHeader}>
                  <Text style={styles.scoreFactorTitle}>{factor.name}</Text>
                  <Text style={styles.scoreFactorValue}>
                    {factor.score}/100
                  </Text>
                </View>
                <View style={styles.scoreFactorProgress}>
                  <View
                    style={[
                      styles.scoreFactorProgressFill,
                      {
                        width: `${factor.score}%`,
                        backgroundColor: factor.color,
                      },
                    ]}
                  />
                </View>
                <Text style={styles.scoreFactorDescription}>
                  {factor.description}
                </Text>
              </View>
            ))}
          </View>

          {/* 智能建议 */}
          <View style={styles.monthlyAnalysisSection}>
            <Text style={styles.monthlyAnalysisSectionTitle}>智能财务建议</Text>

            {suggestions.map((suggestion, index) => (
              <View key={`suggestion-${index}`} style={styles.suggestionItem}>
                <View
                  style={[
                    styles.suggestionIcon,
                    {backgroundColor: suggestion.color},
                  ]}>
                  <Icon name={suggestion.icon} size={20} color="#FFFFFF" />
                </View>
                <View style={styles.suggestionContent}>
                  <Text style={styles.suggestionTitle}>{suggestion.title}</Text>
                  <Text style={styles.suggestionDescription}>
                    {suggestion.description}
                  </Text>
                </View>
              </View>
            ))}
          </View>

          {/* 结语 */}
          <View style={styles.monthlyAnalysisFooter}>
            <Text style={styles.monthlyAnalysisFooterText}>
              希望这份月度分析报告能帮助你更好地了解自己的财务状况，做出更明智的消费决策。
            </Text>
            <Text style={styles.monthlyAnalysisFooterSignature}>
              — 你的智能财务助手
            </Text>
          </View>
        </ScrollView>
      </CustomModal>
    );
  };

  // 重新加载家庭信息
  const loadFamilyInfo = async () => {
    try {
      const familyInfo = await databaseService.getFamilyInfo();
      if (familyInfo) {
        setFamilyName(familyInfo.name);
      }
    } catch (error) {
      console.error('加载家庭信息失败', error);
    }
  };

  // 年月选择器弹窗
  const renderYearMonthPicker = () => {
    useEffect(() => {
      if (yearMonthPickerVisible) {
        setTempYear(dayjs(selectedDate).year());
      }
    }, [yearMonthPickerVisible]);
    return (
      <Modal
        visible={yearMonthPickerVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setYearMonthPickerVisible(false)}>
        <TouchableWithoutFeedback
          onPress={() => setYearMonthPickerVisible(false)}>
          <View
            style={{
              flex: 1,
              backgroundColor: 'rgba(0,0,0,0.3)',
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <View
              style={{
                width: '90%',
                backgroundColor: '#fff',
                borderRadius: 15,
                overflow: 'hidden',
              }}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  padding: 15,
                  backgroundColor: COLORS.secondary,
                }}>
                <Text
                  style={{
                    fontSize: 18,
                    fontWeight: '700',
                    color: COLORS.text.primary,
                  }}>
                  选择年月
                </Text>
                <TouchableOpacity
                  style={{
                    paddingVertical: 6,
                    paddingHorizontal: 12,
                    borderRadius: 15,
                    backgroundColor: COLORS.background.light,
                  }}
                  onPress={() => setYearMonthPickerVisible(false)}>
                  <Text
                    style={{
                      fontSize: 16,
                      color: COLORS.primary,
                      fontWeight: '600',
                    }}>
                    关闭
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={{flexDirection: 'row', padding: 15}}>
                <View style={{flex: 1, marginHorizontal: 5}}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#333',
                      textAlign: 'center',
                      marginBottom: 10,
                    }}>
                    年份
                  </Text>
                  <FlatList
                    data={availableYears}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={{
                          padding: 12,
                          borderBottomWidth: 1,
                          borderBottomColor: '#F0F0F0',
                          alignItems: 'center',
                          backgroundColor:
                            tempYear === item ? COLORS.secondary : undefined,
                        }}
                        onPress={() => setTempYear(item)}>
                        <Text
                          style={{
                            fontSize: 16,
                            color:
                              tempYear === item
                                ? COLORS.primary
                                : COLORS.text.primary,
                            fontWeight: tempYear === item ? '600' : '400',
                          }}>
                          {item}年
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={{maxHeight: 250}}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
                <View style={{flex: 1, marginHorizontal: 5}}>
                  <Text
                    style={{
                      fontSize: 16,
                      fontWeight: '600',
                      color: '#333',
                      textAlign: 'center',
                      marginBottom: 10,
                    }}>
                    月份
                  </Text>
                  <FlatList
                    data={Array.from({length: 12}, (_, i) => i + 1)}
                    renderItem={({item}) => (
                      <TouchableOpacity
                        style={{
                          padding: 12,
                          borderBottomWidth: 1,
                          borderBottomColor: '#F0F0F0',
                          alignItems: 'center',
                          backgroundColor:
                            dayjs(selectedDate).month() + 1 === item &&
                            tempYear === dayjs(selectedDate).year()
                              ? COLORS.secondary
                              : undefined,
                        }}
                        onPress={() => {
                          // 切换年月
                          const newDate = dayjs(selectedDate)
                            .year(tempYear)
                            .month(item - 1)
                            .date(1)
                            .format('YYYY-MM-DD');
                          setSelectedDate(newDate);
                          setYearMonthPickerVisible(false);
                          // 如果有CustomCalendar ref，可以调用其跳转方法
                          if (
                            calendarRef.current &&
                            calendarRef.current.scrollToMonth
                          ) {
                            calendarRef.current.scrollToMonth(
                              new Date(newDate),
                            );
                          }
                        }}>
                        <Text
                          style={{
                            fontSize: 16,
                            color:
                              dayjs(selectedDate).month() + 1 === item &&
                              tempYear === dayjs(selectedDate).year()
                                ? COLORS.primary
                                : COLORS.text.primary,
                            fontWeight:
                              dayjs(selectedDate).month() + 1 === item &&
                              tempYear === dayjs(selectedDate).year()
                                ? '600'
                                : '400',
                          }}>
                          {item}月
                        </Text>
                      </TouchableOpacity>
                    )}
                    keyExtractor={item => item.toString()}
                    style={{maxHeight: 250}}
                    showsVerticalScrollIndicator={false}
                  />
                </View>
              </View>
            </View>
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    );
  };

  // 修改 renderTransactionItem 函数，添加长按事件
  const renderTransactionItem = ({item}) => {
    const isExpense = item.type === 'expense';
    const amountText = isExpense
      ? `-${parseFloat(item.amount).toFixed(2)}`
      : `+${parseFloat(item.amount).toFixed(2)}`;
    const amountColor = isExpense ? COLORS.expense : COLORS.income;

    return (
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => handleTransactionPress(item)}
        onLongPress={() => showActionSheet(item)}
        delayLongPress={500} // 长按触发时间 500ms
      >
        {/* 左侧信息部分保持不变 */}
        <View style={styles.transactionLeft}>
          {/* 您现有的分类图标和名称 */}
          <View
            style={[
              styles.categoryIcon,
              {
                backgroundColor: getCategory(item.categoryId)?.isExpense
                  ? COLORS.expense
                  : COLORS.income,
              },
            ]}>
            <Icon
              name={getCategory(item.categoryId)?.icon || 'snowflake'}
              size={20}
              color="#FFFFFF"
            />
          </View>
          <View style={styles.transactionInfo}>
            <Text style={styles.categoryName}>
              {getCategory(item.categoryId)?.name || '未分类'}
            </Text>
            {item.note ? (
              <Text style={styles.transactionNote} numberOfLines={1}>
                {item.note}
              </Text>
            ) : null}
          </View>
          <View style={{flexDirection: 'row'}}>
            {item.familyName ? (
              <View
                style={[
                  styles.familyNameBadge,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.familyName}>{item.familyName}</Text>
              </View>
            ) : null}
            {/* 待报销标识 - 如果isReimbursable为true则显示 */}
            {item.isReimbursable && (
              <View
                style={[
                  styles.reimbursableTag,
                  {backgroundColor: colors.primary},
                ]}>
                <Text style={styles.reimbursableText}>报销</Text>
              </View>
            )}
          </View>
        </View>

        {/* 右侧金额和待报销标识 */}
        <View style={styles.transactionRight}>
          <Text style={[styles.transactionAmount, {color: amountColor}]}>
            {amountText}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <PageContainer
      headerTitle="我的账本"
      showBackButton={false}
      rightComponent={rightComponent}
      showHeader={false}
      backgroundColor={colors.primary}>
      <CustomCalendar
        ref={calendarRef}
        onSelectDate={handleSelectDate}
        transactionDates={transactionDates}
        initialDate={new Date()}
        // 新增：传递onYearMonthPress回调，仅首页可用
        onYearMonthPress={() => setYearMonthPickerVisible(true)}
      />

      {/* 年月选择器弹窗，仅首页可用 */}
      {renderYearMonthPicker()}

      <View style={styles.transactionsContainer}>
        <View style={styles.toggleContainer}>
          <View style={styles.toggleButton}>
            <TouchableOpacity
              style={[
                styles.toggleOption,
                !showMonthlyStats && {backgroundColor: colors.primary},
              ]}
              onPress={() => setShowMonthlyStats(false)}>
              <Text
                style={[
                  styles.toggleText,
                  !showMonthlyStats && styles.toggleTextActive,
                ]}>
                当日
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.toggleOption,
                showMonthlyStats && {backgroundColor: colors.primary},
              ]}
              onPress={() => setShowMonthlyStats(true)}>
              <Text
                style={[
                  styles.toggleText,
                  showMonthlyStats && styles.toggleTextActive,
                ]}>
                当月
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.dateHeader}>
          <View style={styles.dateContainer}>
            <Text style={styles.dateText}>
              {showMonthlyStats
                ? dayjs(selectedDate).format('YYYY/MM')
                : formatDate(selectedDate)}
            </Text>
          </View>

          <View style={styles.dailyStatsContainer}>
            <Text style={styles.dailyStatsText}>
              收入:{' '}
              <Text style={styles.incomeText}>
                ￥
                {showMonthlyStats
                  ? monthlyStats.income.toFixed(2)
                  : dailyStats.income.toFixed(2)}
              </Text>
            </Text>
            <Text style={styles.dailyStatsText}>
              支出:{' '}
              <Text style={styles.expenseText}>
                ￥
                {showMonthlyStats
                  ? monthlyStats.expense.toFixed(2)
                  : dailyStats.expense.toFixed(2)}
              </Text>
            </Text>
          </View>
        </View>

        {loading ? (
          renderLoading()
        ) : transactions.length > 0 ? (
          <FlatList
            data={transactions}
            renderItem={renderTransactionItem}
            keyExtractor={item =>
              item.id?.toString() || Math.random().toString()
            }
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>当日没有记账记录</Text>
          </View>
        )}
      </View>

      <View style={{backgroundColor: COLORS.background.white}}>
        <CustomTabBar
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={handleTabPress}
          onLongPress={handleTabLongPress}
        />
      </View>

      {/* 语音记账页面 */}
      <VoiceRecordingScreen
        visible={voiceRecordingVisible}
        onClose={handleCloseVoiceRecording}
        onConfirm={handleVoiceResult}
      />

      {/* 语音引导模态框 */}
      <VoiceGuideModal
        visible={voiceGuideVisible}
        onClose={handleCloseVoiceGuide}
      />

      {/* 渲染月度账单分析通知 */}
      {renderMonthlyAnalysisNotice()}

      {/* 渲染月度账单分析 */}
      {renderMonthlyAnalysis()}
      {/* 确认对话框 */}
      <ConfirmDialog
        visible={confirmDialogVisible}
        title="确认"
        message={confirmDialogMessage}
        onConfirm={() => {
          setConfirmDialogVisible(false);
          confirmDialogAction();
        }}
        onCancel={() => {
          setConfirmDialogVisible(false);
        }}
        confirmText="确定"
      />
    </PageContainer>
  );
};

const styles = StyleSheet.create({
  transactionsContainer: {
    flex: 1,
    backgroundColor: COLORS.background.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 15,
    paddingBottom: 0,
  },
  toggleContainer: {
    alignItems: 'center',
    marginBottom: 14,
  },
  toggleButton: {
    flexDirection: 'row',
    backgroundColor: '#F2F2F7',
    borderRadius: 15,
    height: 30,
    width: 130,
    padding: 2,
  },
  toggleOption: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 13,
  },
  toggleOptionActive: {},
  toggleText: {
    fontSize: 13,
    color: '#8E8E93',
    fontWeight: '500',
  },
  toggleTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  dateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 10,
    // borderBottomWidth: 1,
    // borderBottomColor: '#EEEEEE',
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 6,
  },
  dateText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  dailyStatsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dailyStatsText: {
    fontSize: 14,
    color: COLORS.text.primary,
    marginLeft: 10,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: 12,
    paddingVertical: 12,
    borderColor: COLORS.border.medium,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionRight: {
    width: 100,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  transactionNote: {
    fontSize: 14,
    color: '#8E8E93',
  },
  transactionAmount: {
    fontSize: 18,
    fontWeight: '700',
  },
  familyNameBadge: {
    marginRight: 20,
    backgroundColor: '#FFF9C4',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  familyName: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  incomeText: {
    color: COLORS.text.primary,
  },
  expenseText: {
    color: COLORS.text.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 30,
  },
  emptyText: {
    fontSize: 16,
    color: '#999999',
  },
  calendarHeader: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2d4150',
  },
  headerButton: {
    fontSize: 16,
    color: '#007AFF',
  },
  monthlyAnalysisModalStyle: {
    padding: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: Dimensions.get('window').height * 0.8,
  },
  monthlyAnalysisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  monthlyAnalysisHeaderLeft: {
    flex: 1,
  },
  monthlyAnalysisTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
  },
  monthlyAnalysisSubtitle: {
    fontSize: 14,
    color: '#666666',
    marginTop: 4,
  },
  monthlyAnalysisCloseButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  monthlyAnalysisContent: {
    flex: 1,
    padding: 20,
  },
  monthlyAnalysisCover: {
    alignItems: 'center',
    marginBottom: 30,
  },
  monthlyAnalysisCoverTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 15,
  },
  monthlyAnalysisCoverSubtitle: {
    fontSize: 16,
    color: '#666666',
    marginTop: 30,
    marginBottom: 10,
  },
  healthScoreContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  healthScoreText: {
    fontSize: 48,
    fontWeight: '700',
    color: '#007AFF',
  },
  healthScoreMax: {
    fontSize: 20,
    color: '#999999',
    marginBottom: 8,
  },
  healthScoreDesc: {
    marginTop: 10,
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 15,
  },
  healthScoreDescText: {
    fontSize: 14,
    color: '#007AFF',
    textAlign: 'center',
  },
  monthlyAnalysisSection: {
    marginBottom: 25,
  },
  monthlyAnalysisSectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 15,
  },
  monthlyAnalysisSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666666',
    marginTop: 20,
    marginBottom: 10,
  },
  monthlyAnalysisSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 10,
  },
  monthlyAnalysisSummaryItem: {
    alignItems: 'center',
  },
  monthlyAnalysisSummaryValue: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
  },
  monthlyAnalysisSummaryLabel: {
    fontSize: 14,
    color: '#666666',
    marginTop: 5,
  },
  positiveBalance: {
    color: '#4CAF50',
  },
  negativeBalance: {
    color: '#FF3B30',
  },
  behaviorTraitContainer: {
    marginTop: 10,
  },
  categoryItem: {
    marginBottom: 15,
  },
  categoryProgress: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    marginBottom: 5,
    overflow: 'hidden',
  },
  categoryProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  categoryInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
  },
  categoryName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
  },
  categoryPercentage: {
    fontSize: 14,
    color: '#666666',
  },
  categoryAmount: {
    fontSize: 12,
    color: '#999999',
  },
  traitsContainer: {
    marginTop: 10,
  },
  traitItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 12,
  },
  traitIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  traitContent: {
    flex: 1,
  },
  traitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 5,
  },
  traitDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  scoreFactorItem: {
    marginBottom: 15,
  },
  scoreFactorHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  scoreFactorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  scoreFactorValue: {
    fontSize: 16,
    fontWeight: '700',
    color: '#007AFF',
  },
  scoreFactorProgress: {
    height: 8,
    backgroundColor: '#F0F0F0',
    borderRadius: 4,
    marginBottom: 8,
    overflow: 'hidden',
  },
  scoreFactorProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  scoreFactorDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  suggestionItem: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#F8F8F8',
    borderRadius: 10,
    padding: 15,
  },
  suggestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 5,
  },
  suggestionDescription: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  monthlyAnalysisFooter: {
    marginTop: 10,
    marginBottom: 30,
    padding: 20,
    backgroundColor: '#F8F8F8',
    borderRadius: 15,
  },
  monthlyAnalysisFooterText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  monthlyAnalysisFooterSignature: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'right',
    marginTop: 10,
  },
  noticeModalStyle: {
    padding: 25,
    borderRadius: 20,
    maxWidth: 320,
    alignItems: 'center',
  },
  noticeContainer: {
    alignItems: 'center',
  },
  noticeEmoji: {
    fontSize: 50,
    marginBottom: 15,
  },
  noticeTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 10,
    textAlign: 'center',
  },
  noticeSubtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 20,
    textAlign: 'center',
  },
  noticeScoreContainer: {
    alignItems: 'center',
    marginBottom: 25,
  },
  noticeScoreLabel: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  noticeScoreCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F8F8F8',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  noticeScoreValue: {
    fontSize: 30,
    fontWeight: '700',
  },
  noticeMessage: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  noticeButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 12,
    paddingHorizontal: 25,
    borderRadius: 25,
    marginBottom: 10,
    width: '100%',
    alignItems: 'center',
  },
  noticeButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  noticeLaterButton: {
    paddingVertical: 12,
  },
  noticeLaterButtonText: {
    color: '#666666',
    fontSize: 14,
  },
  reimbursableTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 10, // 与金额的间距
  },
  reimbursableText: {
    fontSize: 12,
    color: '#FFFFFF', // 待报销标签的文字颜色
    fontWeight: '600',
  },
});

export default HomeScreen;
