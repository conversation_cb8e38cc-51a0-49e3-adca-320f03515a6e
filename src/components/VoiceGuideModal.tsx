import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Dimensions,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import {useTheme} from '../context/ThemeContext';

const {width} = Dimensions.get('window');

interface VoiceGuideModalProps {
  visible: boolean;
  onClose: () => void;
}

const VoiceGuideModal: React.FC<VoiceGuideModalProps> = ({
  visible,
  onClose,
}) => {
  const {colors} = useTheme();

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={[styles.container, {backgroundColor: colors.card}]}>
          {/* 标题 */}
          <View style={styles.header}>
            <Icon name="microphone" size={32} color={colors.primary} />
            <Text style={[styles.title, {color: colors.text}]}>
              🎤 新功能：语音记账
            </Text>
          </View>

          {/* 说明内容 */}
          <View style={styles.content}>
            <View style={styles.instructionItem}>
              <View style={[styles.iconContainer, {backgroundColor: colors.primary + '20'}]}>
                <Icon name="hand-pointer" size={20} color={colors.primary} />
              </View>
              <View style={styles.textContainer}>
                <Text style={[styles.instructionTitle, {color: colors.text}]}>
                  点击 ➕ 按钮
                </Text>
                <Text style={[styles.instructionDesc, {color: colors.textSecondary}]}>
                  进入手动记账页面
                </Text>
              </View>
            </View>

            <View style={styles.instructionItem}>
              <View style={[styles.iconContainer, {backgroundColor: '#ff6b6b20'}]}>
                <Icon name="hand-back-fist" size={20} color="#ff6b6b" />
              </View>
              <View style={styles.textContainer}>
                <Text style={[styles.instructionTitle, {color: colors.text}]}>
                  长按 ➕ 按钮
                </Text>
                <Text style={[styles.instructionDesc, {color: colors.textSecondary}]}>
                  启动语音记账功能
                </Text>
              </View>
            </View>

            <View style={styles.exampleContainer}>
              <Text style={[styles.exampleTitle, {color: colors.primary}]}>
                💡 语音示例：
              </Text>
              <View style={[styles.exampleItem, {backgroundColor: colors.background}]}>
                <Text style={[styles.exampleText, {color: colors.text}]}>
                  "记账 50元 午餐"
                </Text>
              </View>
              <View style={[styles.exampleItem, {backgroundColor: colors.background}]}>
                <Text style={[styles.exampleText, {color: colors.text}]}>
                  "收入 3000元 工资"
                </Text>
              </View>
              <View style={[styles.exampleItem, {backgroundColor: colors.background}]}>
                <Text style={[styles.exampleText, {color: colors.text}]}>
                  "花了 25元 打车"
                </Text>
              </View>
            </View>

            <Text style={[styles.note, {color: colors.textSecondary}]}>
              系统会自动识别金额、分类和收支类型
            </Text>
          </View>

          {/* 按钮 */}
          <TouchableOpacity
            style={[styles.button, {backgroundColor: colors.primary}]}
            onPress={onClose}>
            <Text style={styles.buttonText}>我知道了</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },

  container: {
    width: width - 40,
    borderRadius: 16,
    padding: 24,
    elevation: 10,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },

  header: {
    alignItems: 'center',
    marginBottom: 24,
  },

  title: {
    fontSize: 20,
    fontWeight: '700',
    marginTop: 12,
    textAlign: 'center',
  },

  content: {
    marginBottom: 24,
  },

  instructionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },

  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },

  textContainer: {
    flex: 1,
  },

  instructionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },

  instructionDesc: {
    fontSize: 14,
  },

  exampleContainer: {
    marginTop: 20,
    marginBottom: 16,
  },

  exampleTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },

  exampleItem: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 6,
  },

  exampleText: {
    fontSize: 14,
    fontStyle: 'italic',
  },

  note: {
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 18,
  },

  button: {
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },

  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default VoiceGuideModal;
