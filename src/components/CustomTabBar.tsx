import React, {useState, useEffect, useRef} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Platform,
  Animated,
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome6';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { COLORS } from '../utils/color';
import { useTheme } from '../context/ThemeContext';
interface TabItem {
  key: string;
  title: string;
  icon: string; // FontAwesome6 图标名称
}

interface CustomTabBarProps {
  tabs: TabItem[];
  activeTab: string;
  onTabPress: (tabKey: string) => void;
  onLongPress?: (tabKey: string) => void;
}

const CustomTabBar: React.FC<CustomTabBarProps> = ({
  tabs,
  activeTab,
  onTabPress,
  onLongPress,
}) => {
  const {colors} = useTheme();

  // 动画和状态
  const [showVoiceHint, setShowVoiceHint] = useState(false);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const breathingAnim = useRef(new Animated.Value(0.3)).current;
  const rippleScale = useRef(new Animated.Value(0)).current;
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  // 初始化动画和提示
  useEffect(() => {
    // 检查是否显示语音提示
    checkVoiceHintVisibility();

    // 呼吸动画
    const breathing = Animated.loop(
      Animated.sequence([
        Animated.timing(breathingAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(breathingAnim, {
          toValue: 0.3,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );

    if (showVoiceHint) {
      breathing.start();

      // 5秒后隐藏提示
      setTimeout(() => {
        setShowVoiceHint(false);
        AsyncStorage.setItem('hasSeenVoiceHint', 'true');
      }, 5000);
    }

    return () => {
      breathing.stop();
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current);
      }
    };
  }, [showVoiceHint]);

  // 检查是否显示语音提示
  const checkVoiceHintVisibility = async () => {
    try {
      const hasSeenHint = await AsyncStorage.getItem('hasSeenVoiceHint');
      if (!hasSeenHint) {
        setShowVoiceHint(true);
      }
    } catch (error) {
      console.error('检查语音提示状态失败:', error);
    }
  };

  // 处理长按开始
  const handlePressIn = () => {
    longPressTimer.current = setTimeout(() => {
      setIsLongPressing(true);

      // 开始波纹动画
      Animated.timing(rippleScale, {
        toValue: 2,
        duration: 800,
        useNativeDriver: true,
      }).start();

      // 触发长按回调
      if (onLongPress) {
        onLongPress('voice');
      }
    }, 800); // 800ms 长按延迟
  };

  // 处理按压结束
  const handlePressOut = () => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current);
      longPressTimer.current = null;
    }

    if (!isLongPressing) {
      // 短按，执行正常的添加功能
      onTabPress('add');
    }

    // 重置状态和动画
    setIsLongPressing(false);
    Animated.timing(rippleScale, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  // 渲染中间的添加按钮
  const renderAddButton = () => {
    return (
      <TouchableOpacity
        style={[styles.addButton, {backgroundColor: colors.primary}]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.8}>

        {/* 波纹效果 */}
        <Animated.View
          style={[
            styles.ripple,
            {
              backgroundColor: colors.primary + '30',
              transform: [{scale: rippleScale}]
            }
          ]}
        />

        {/* 主按钮 */}
        <View style={styles.addButtonInner}>
          <Icon name="plus" size={30} color="#fff" />
        </View>

        {/* 语音提示小图标 */}
        {showVoiceHint && (
          <Animated.View style={[styles.voiceHint, {opacity: breathingAnim}]}>
            <Icon name="microphone" size={12} color="#fff" />
          </Animated.View>
        )}
      </TouchableOpacity>
    );
  };

  // 渲染普通的 Tab 按钮
  const renderTabButton = (tab: TabItem) => {
    const isActive = activeTab === tab.key;

    return (
      <TouchableOpacity
        key={tab.key}
        style={[
          styles.tabButton,
          // isActive && styles.activeTabButton,
        ]}
        onPress={() => onTabPress(tab.key)}
        activeOpacity={0.7}>
        <Icon
          name={tab.icon}
          size={22}
          // color={getIconColor(tab.key)}
          color={colors.primary}
          style={styles.tabIcon}
        />
        <Text
          style={[
            styles.tabText,
            // { color: getTextColor(tab.key) },
            {color: colors.primary},
          ]}>
          {tab.title}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabBar}>
        {/* 左侧 Tab */}
        <View style={styles.tabSide}>{renderTabButton(tabs[0])}</View>

        {/* 中间的添加按钮 */}
        {renderAddButton()}

        {/* 右侧 Tab */}
        <View style={styles.tabSide}>{renderTabButton(tabs[2])}</View>
      </View>

      {/* 底部安全区域填充 */}
      {Platform.OS === 'ios' && <View style={styles.safeAreaFill} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: COLORS.secondary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 10,
    paddingTop: 10,
  },
  tabBar: {
    flexDirection: 'row',
    height: 60,
    alignItems: 'center',
    justifyContent: 'space-around',
    paddingHorizontal: 10,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -10,
  },
  tabSide: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
  },
  activeTabButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  tabIcon: {
    marginBottom: 4,
  },
  tabText: {
    fontSize: 12,
    fontWeight: '500',
  },
  addButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    transform: [{translateY: -20}],
  },
  addButtonInner: {
    width: 52,
    height: 52,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  safeAreaFill: {
    height: Platform.OS === 'ios' ? 20 : 0,
  },

  // 新增样式
  ripple: {
    position: 'absolute',
    width: 60,
    height: 60,
    borderRadius: 30,
  },

  voiceHint: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#ff6b6b',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default CustomTabBar;
