{"name": "snow", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/raw"}, "dependencies": {"@ant-design/icons-react-native": "^2.3.2", "@ant-design/react-native": "^5.3.2", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-documents/picker": "^10.1.1", "@react-native-voice/voice": "^3.2.4", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/native": "^7.0.18", "@react-navigation/stack": "^7.2.2", "@shopify/react-native-skia": "^1.12.2", "dayjs": "^1.11.13", "events": "^3.3.0", "react": "19.0.0", "react-native": "0.78.1", "react-native-ble-manager": "^12.1.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.24.0", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-notifications": "^5.1.0", "react-native-permissions": "^5.3.0", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.9.2", "react-native-share": "^12.0.9", "react-native-sqlite-storage": "^6.0.1", "react-native-svg": "^15.11.2", "react-native-tcp-socket": "^6.3.0", "react-native-udp": "^4.1.7", "react-native-vector-icons": "^10.2.0", "victory-native": "^37.3.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.1", "@react-native/eslint-config": "0.78.1", "@react-native/metro-config": "0.78.1", "@react-native/typescript-config": "0.78.1", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native": "^0.72.8", "@types/react-native-sqlite-storage": "^6.0.5", "@types/react-test-renderer": "^19.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}